import React, { useState } from 'react';


import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  FileText,
  Download
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import {
  useFieldReports
} from '@/hooks/field-staff/useFieldStaffAttendance';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import AdminStaffReportsTable from '../field-staff/AdminStaffReportsTable';

const ConsolidatedStaffReports = () => {
  const { profile } = useAuth();

  const [dateFilter, setDateFilter] = useState(new Date().toISOString().split('T')[0]);
  const [staffFilter, setStaffFilter] = useState('all');

  // Data fetching
  const { data: fieldReports } = useFieldReports(staffFilter === 'all' ? undefined : staffFilter, dateFilter, dateFilter);

  // Role-based permissions
  const canViewAllReports = profile?.role === 'admin' || profile?.role === 'program_officer';

  return (
    <PageLayout>
      <PageHeader
        title="Field Staff Reports"
        description="View, edit, and manage all field reports submitted by staff members"
        icon={FileText}
      />

      <ContentCard>
        <div className="space-y-6">
          {/* Header with filters */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              <h3 className="text-lg font-semibold">Field Staff Reports</h3>
              {fieldReports && fieldReports.length > 0 && (
                <Badge variant="secondary">
                  {fieldReports.length}
                </Badge>
              )}
            </div>

            {/* Filters */}
            <div className="flex items-center gap-2">
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="w-40"
              />

              {canViewAllReports && (
                <Select value={staffFilter} onValueChange={setStaffFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="All Staff" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Staff</SelectItem>
                    {/* Add staff options dynamically */}
                  </SelectContent>
                </Select>
              )}

              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              View, edit, and manage all field reports submitted by staff members
            </p>

            {/* Use the AdminStaffReportsTable component */}
            <AdminStaffReportsTable />
          </div>
        </div>
      </ContentCard>
    </PageLayout>
  );
};

export default ConsolidatedStaffReports;
